/******************************************************************************
 *                                                                            *
 * Copyright (C) 2021 by <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>             *
 * Copyright (C) 2021 by Max Lv <<EMAIL>>                          *
 * Copyright (C) 2021 by Mygod Studio <<EMAIL>>  *
 *                                                                            *
 * This program is free software: you can redistribute it and/or modify       *
 * it under the terms of the GNU General Public License as published by       *
 * the Free Software Foundation, either version 3 of the License, or          *
 *  (at your option) any later version.                                       *
 *                                                                            *
 * This program is distributed in the hope that it will be useful,            *
 * but WITHOUT ANY WARRANTY; without even the implied warranty of             *
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the              *
 * GNU General Public License for more details.                               *
 *                                                                            *
 * You should have received a copy of the GNU General Public License          *
 * along with this program. If not, see <http://www.gnu.org/licenses/>.       *
 *                                                                            *
 ******************************************************************************/

package com.mohamedrady.v2hoor.plugin

import android.graphics.drawable.Drawable

abstract class Plugin {
    abstract val id: String
    abstract val label: CharSequence
    abstract val version: Int
    abstract val versionName: String
    open val icon: Drawable? get() = null
    open val defaultConfig: String? get() = null
    open val packageName: String get() = ""
    open val directBootAware: Boolean get() = true

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false
        return id == (other as Plugin).id
    }

    override fun hashCode() = id.hashCode()
}
