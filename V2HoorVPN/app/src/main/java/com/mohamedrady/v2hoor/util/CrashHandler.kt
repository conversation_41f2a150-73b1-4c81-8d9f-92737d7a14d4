package com.mohamedrady.v2hoor.util

import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import android.util.Log
import java.io.File
import java.io.FileWriter
import java.io.PrintWriter
import java.io.StringWriter
import java.text.SimpleDateFormat
import java.util.*

class CrashHandler private constructor() : Thread.UncaughtExceptionHandler {
    
    companion object {
        private const val TAG = "CrashHandler"
        private const val CRASH_LOG_DIR = "crash_logs"
        private const val MAX_LOG_FILES = 10
        
        @Volatile
        private var instance: CrashHandler? = null
        
        fun getInstance(): CrashHandler {
            return instance ?: synchronized(this) {
                instance ?: CrashHandler().also { instance = it }
            }
        }
        
        fun init(context: Context) {
            val crashHandler = getInstance()
            crashHandler.context = context.applicationContext
            Thread.setDefaultUncaughtExceptionHandler(crashHandler)
            Log.i(TAG, "CrashHandler initialized")
        }
    }
    
    private var context: Context? = null
    private var defaultHandler: Thread.UncaughtExceptionHandler? = null
    
    init {
        defaultHandler = Thread.getDefaultUncaughtExceptionHandler()
    }
    
    override fun uncaughtException(thread: Thread, ex: Throwable) {
        try {
            Log.e(TAG, "Uncaught exception in thread ${thread.name}", ex)
            
            // Save crash log to file
            saveCrashLog(ex, thread)
            
            // Log detailed crash information
            logCrashDetails(ex, thread)
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to handle crash", e)
        } finally {
            // Call the default handler
            defaultHandler?.uncaughtException(thread, ex)
        }
    }
    
    private fun saveCrashLog(ex: Throwable, thread: Thread) {
        try {
            val context = this.context ?: return
            
            // Create crash logs directory
            val crashDir = File(context.filesDir, CRASH_LOG_DIR)
            if (!crashDir.exists()) {
                crashDir.mkdirs()
            }
            
            // Clean old log files
            cleanOldLogFiles(crashDir)
            
            // Create crash log file
            val timestamp = SimpleDateFormat("yyyy-MM-dd_HH-mm-ss", Locale.getDefault()).format(Date())
            val logFile = File(crashDir, "crash_$timestamp.log")
            
            FileWriter(logFile).use { writer ->
                writer.write(generateCrashReport(ex, thread))
            }
            
            Log.i(TAG, "Crash log saved to: ${logFile.absolutePath}")
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to save crash log", e)
        }
    }
    
    private fun generateCrashReport(ex: Throwable, thread: Thread): String {
        val context = this.context ?: return "Context not available"
        
        val report = StringBuilder()
        
        // App info
        report.append("=== CRASH REPORT ===\n")
        report.append("Time: ${SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date())}\n")
        report.append("Thread: ${thread.name}\n\n")
        
        // App version info
        try {
            val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
            report.append("=== APP INFO ===\n")
            report.append("Package: ${packageInfo.packageName}\n")
            report.append("Version: ${packageInfo.versionName} (${packageInfo.versionCode})\n\n")
        } catch (e: PackageManager.NameNotFoundException) {
            report.append("=== APP INFO ===\n")
            report.append("Package info not available\n\n")
        }
        
        // Device info
        report.append("=== DEVICE INFO ===\n")
        report.append("Brand: ${Build.BRAND}\n")
        report.append("Model: ${Build.MODEL}\n")
        report.append("Manufacturer: ${Build.MANUFACTURER}\n")
        report.append("Device: ${Build.DEVICE}\n")
        report.append("Product: ${Build.PRODUCT}\n")
        report.append("Hardware: ${Build.HARDWARE}\n")
        report.append("Board: ${Build.BOARD}\n")
        report.append("Display: ${Build.DISPLAY}\n")
        report.append("Fingerprint: ${Build.FINGERPRINT}\n")
        report.append("Host: ${Build.HOST}\n")
        report.append("ID: ${Build.ID}\n")
        report.append("Tags: ${Build.TAGS}\n")
        report.append("Type: ${Build.TYPE}\n")
        report.append("User: ${Build.USER}\n\n")
        
        // OS info
        report.append("=== OS INFO ===\n")
        report.append("Android Version: ${Build.VERSION.RELEASE}\n")
        report.append("API Level: ${Build.VERSION.SDK_INT}\n")
        report.append("Codename: ${Build.VERSION.CODENAME}\n")
        report.append("Incremental: ${Build.VERSION.INCREMENTAL}\n\n")
        
        // Memory info
        report.append("=== MEMORY INFO ===\n")
        val runtime = Runtime.getRuntime()
        report.append("Max Memory: ${runtime.maxMemory() / 1024 / 1024} MB\n")
        report.append("Total Memory: ${runtime.totalMemory() / 1024 / 1024} MB\n")
        report.append("Free Memory: ${runtime.freeMemory() / 1024 / 1024} MB\n")
        report.append("Used Memory: ${(runtime.totalMemory() - runtime.freeMemory()) / 1024 / 1024} MB\n\n")
        
        // Exception details
        report.append("=== EXCEPTION ===\n")
        report.append("Exception: ${ex.javaClass.name}\n")
        report.append("Message: ${ex.message}\n")
        report.append("Cause: ${ex.cause}\n\n")
        
        // Stack trace
        report.append("=== STACK TRACE ===\n")
        val stringWriter = StringWriter()
        val printWriter = PrintWriter(stringWriter)
        ex.printStackTrace(printWriter)
        report.append(stringWriter.toString())
        
        return report.toString()
    }
    
    private fun logCrashDetails(ex: Throwable, thread: Thread) {
        Log.e(TAG, "=== CRASH DETAILS ===")
        Log.e(TAG, "Thread: ${thread.name}")
        Log.e(TAG, "Exception: ${ex.javaClass.name}")
        Log.e(TAG, "Message: ${ex.message}")
        Log.e(TAG, "Cause: ${ex.cause}")
        
        // Log stack trace
        val stringWriter = StringWriter()
        val printWriter = PrintWriter(stringWriter)
        ex.printStackTrace(printWriter)
        Log.e(TAG, "Stack trace:\n${stringWriter}")
    }
    
    private fun cleanOldLogFiles(crashDir: File) {
        try {
            val logFiles = crashDir.listFiles { file -> file.name.startsWith("crash_") && file.name.endsWith(".log") }
            if (logFiles != null && logFiles.size > MAX_LOG_FILES) {
                // Sort by last modified time and delete oldest files
                logFiles.sortBy { it.lastModified() }
                for (i in 0 until logFiles.size - MAX_LOG_FILES) {
                    logFiles[i].delete()
                    Log.d(TAG, "Deleted old crash log: ${logFiles[i].name}")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to clean old log files", e)
        }
    }
    
    fun getCrashLogs(context: Context): List<File> {
        val crashDir = File(context.filesDir, CRASH_LOG_DIR)
        if (!crashDir.exists()) {
            return emptyList()
        }
        
        val logFiles = crashDir.listFiles { file -> 
            file.name.startsWith("crash_") && file.name.endsWith(".log") 
        }
        
        return logFiles?.sortedByDescending { it.lastModified() } ?: emptyList()
    }
    
    fun getLatestCrashLog(context: Context): File? {
        return getCrashLogs(context).firstOrNull()
    }
    
    fun clearCrashLogs(context: Context): Boolean {
        return try {
            val crashDir = File(context.filesDir, CRASH_LOG_DIR)
            if (crashDir.exists()) {
                crashDir.deleteRecursively()
            }
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to clear crash logs", e)
            false
        }
    }
}
