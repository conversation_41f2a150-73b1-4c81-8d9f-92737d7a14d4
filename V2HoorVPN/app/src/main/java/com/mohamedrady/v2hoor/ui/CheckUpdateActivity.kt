package com.mohamedrady.v2hoor.ui

import android.os.Bundle
import androidx.appcompat.app.AlertDialog
import androidx.lifecycle.lifecycleScope
import com.mohamedrady.v2hoor.AppConfig
import com.mohamedrady.v2hoor.BuildConfig
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.databinding.ActivityCheckUpdateBinding
import com.mohamedrady.v2hoor.dto.CheckUpdateResult
import com.mohamedrady.v2hoor.extension.toast
import com.mohamedrady.v2hoor.extension.toastSuccess
import com.mohamedrady.v2hoor.handler.MmkvManager
import com.mohamedrady.v2hoor.handler.SpeedtestManager
import com.mohamedrady.v2hoor.handler.UpdateCheckerManager
import com.mohamedrady.v2hoor.util.Utils
import kotlinx.coroutines.launch

class CheckUpdateActivity : BaseActivity() {

    private val binding by lazy { ActivityCheckUpdateBinding.inflate(layoutInflater) }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)

        title = getString(R.string.update_check_for_update)

        binding.layoutCheckUpdate.setOnClickListener {
            checkForUpdates(binding.checkPreRelease.isChecked)
        }

        binding.checkPreRelease.setOnCheckedChangeListener { _, isChecked ->
            MmkvManager.encodeSettings(AppConfig.PREF_CHECK_UPDATE_PRE_RELEASE, isChecked)
        }
        binding.checkPreRelease.isChecked = MmkvManager.decodeSettingsBool(AppConfig.PREF_CHECK_UPDATE_PRE_RELEASE, false)

        "v${BuildConfig.VERSION_NAME} (${SpeedtestManager.getLibVersion()})".also {
            binding.tvVersion.text = it
        }

        checkForUpdates(binding.checkPreRelease.isChecked)
    }

    private fun checkForUpdates(includePreRelease: Boolean) {
        toast(R.string.update_checking_for_update)

        lifecycleScope.launch {
            val result = UpdateCheckerManager.checkForUpdate(includePreRelease)
            if (result.hasUpdate) {
                showUpdateDialog(result)
            } else {
                toastSuccess(R.string.update_already_latest_version)
            }
        }
    }

    private fun showUpdateDialog(result: CheckUpdateResult) {
        AlertDialog.Builder(this)
            .setTitle(getString(R.string.update_new_version_found, result.latestVersion))
            .setMessage(result.releaseNotes)
            .setPositiveButton(R.string.update_now) { _, _ ->
                result.downloadUrl?.let {
                    Utils.openUri(this, it)
                }
            }
            .setNegativeButton(android.R.string.cancel, null)
            .show()
    }
}