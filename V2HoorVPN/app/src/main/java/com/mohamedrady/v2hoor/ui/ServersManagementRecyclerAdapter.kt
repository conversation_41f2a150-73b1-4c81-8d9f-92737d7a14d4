package com.mohamedrady.v2hoor.ui

import android.content.Intent
import android.graphics.Color
import android.text.TextUtils
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AlertDialog
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.RecyclerView
import com.mohamedrady.v2hoor.AngApplication.Companion.application
import com.mohamedrady.v2hoor.AppConfig
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.databinding.ItemQrcodeBinding
import com.mohamedrady.v2hoor.databinding.ItemRecyclerFooterBinding
import com.mohamedrady.v2hoor.databinding.ItemRecyclerMainBinding
import com.mohamedrady.v2hoor.dto.EConfigType
import com.mohamedrady.v2hoor.dto.ProfileItem
import com.mohamedrady.v2hoor.extension.toast
import com.mohamedrady.v2hoor.extension.toastError
import com.mohamedrady.v2hoor.extension.toastSuccess
import com.mohamedrady.v2hoor.handler.AngConfigManager
import com.mohamedrady.v2hoor.handler.MmkvManager
import com.mohamedrady.v2hoor.handler.SettingsManager
import com.mohamedrady.v2hoor.handler.V2rayConfigManager
import com.mohamedrady.v2hoor.helper.ItemTouchHelperAdapter
import com.mohamedrady.v2hoor.helper.ItemTouchHelperViewHolder
import com.mohamedrady.v2hoor.service.V2RayServiceManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class ServersManagementRecyclerAdapter(val activity: ServersManagementActivity) : RecyclerView.Adapter<ServersManagementRecyclerAdapter.BaseViewHolder>(), ItemTouchHelperAdapter {
    companion object {
        private const val VIEW_TYPE_ITEM = 1
        private const val VIEW_TYPE_FOOTER = 2
    }

    private var mActivity: ServersManagementActivity = activity
    private val share_method: Array<out String> by lazy {
        mActivity.resources.getStringArray(R.array.share_method)
    }
    private val share_method_more: Array<out String> by lazy {
        mActivity.resources.getStringArray(R.array.share_method_more)
    }
    var isRunning = false
    private val doubleColumnDisplay = MmkvManager.decodeSettingsBool(AppConfig.PREF_DOUBLE_COLUMN_DISPLAY, false)

    override fun getItemCount() = mActivity.mainViewModel.serversCache.size + 1

    override fun onBindViewHolder(holder: BaseViewHolder, position: Int) {
        if (holder is MainViewHolder) {
            val guid = mActivity.mainViewModel.serversCache[position].guid
            val profile = mActivity.mainViewModel.serversCache[position].profile
            val isCustom = profile.configType == EConfigType.CUSTOM

            holder.itemView.setBackgroundColor(Color.TRANSPARENT)

            //Name address
            holder.itemMainBinding.tvName.text = profile.remarks
            holder.itemMainBinding.tvStatistics.text = getAddress(profile)
            holder.itemMainBinding.tvType.text = profile.configType.name

            //TestResult
            val aff = MmkvManager.decodeServerAffiliationInfo(guid)
            holder.itemMainBinding.tvTestResult.text = aff?.getTestDelayString().orEmpty()
            if ((aff?.testDelayMillis ?: 0L) < 0L) {
                holder.itemMainBinding.tvTestResult.setTextColor(ContextCompat.getColor(mActivity, R.color.colorPingRed))
            } else {
                holder.itemMainBinding.tvTestResult.setTextColor(ContextCompat.getColor(mActivity, R.color.colorPing))
            }

            //layoutIndicator
            if (guid == MmkvManager.getSelectServer()) {
                holder.itemMainBinding.layoutIndicator.setBackgroundResource(R.color.colorAccent)
            } else {
                holder.itemMainBinding.layoutIndicator.setBackgroundResource(0)
            }

            //subscription remarks
            val subRemarks = getSubscriptionRemarks(profile)
            holder.itemMainBinding.tvSubscription.text = subRemarks
            holder.itemMainBinding.layoutSubscription.visibility = if (subRemarks.isEmpty()) View.GONE else View.VISIBLE

            //layout
            if (doubleColumnDisplay) {
                holder.itemMainBinding.layoutShare.visibility = View.GONE
                holder.itemMainBinding.layoutEdit.visibility = View.GONE
                holder.itemMainBinding.layoutRemove.visibility = View.GONE
                holder.itemMainBinding.layoutMore.visibility = View.VISIBLE

                //share method
                val shareOptions = if (isCustom) share_method_more.asList().takeLast(3) else share_method_more.asList()

                holder.itemMainBinding.layoutMore.setOnClickListener {
                    shareServer(guid, profile, position, shareOptions, if (isCustom) 2 else 0)
                }
            } else {
                holder.itemMainBinding.layoutShare.visibility = View.VISIBLE
                holder.itemMainBinding.layoutEdit.visibility = View.VISIBLE
                holder.itemMainBinding.layoutRemove.visibility = View.VISIBLE
                holder.itemMainBinding.layoutMore.visibility = View.GONE

                //share method
                val shareOptions = if (isCustom) share_method.asList().takeLast(1) else share_method.asList()

                holder.itemMainBinding.layoutShare.setOnClickListener {
                    shareServer(guid, profile, position, shareOptions, if (isCustom) 2 else 0)
                }

                holder.itemMainBinding.layoutEdit.setOnClickListener {
                    editServer(guid, profile)
                }
                holder.itemMainBinding.layoutRemove.setOnClickListener {
                    removeServer(guid, position)
                }
            }

            holder.itemMainBinding.infoContainer.setOnClickListener {
                setSelectServer(guid)
            }
        }
    }

    private fun getAddress(profile: ProfileItem): String {
        return when (profile.configType) {
            EConfigType.VMESS, EConfigType.VLESS -> "${profile.server}:${profile.serverPort}"
            EConfigType.SHADOWSOCKS -> "${profile.server}:${profile.serverPort}"
            EConfigType.SOCKS -> "${profile.server}:${profile.serverPort}"
            EConfigType.TROJAN -> "${profile.server}:${profile.serverPort}"
            EConfigType.WIREGUARD -> profile.server.orEmpty()
            EConfigType.HYSTERIA2 -> "${profile.server}:${profile.serverPort}"
            else -> ""
        }
    }

    private fun getSubscriptionRemarks(profile: ProfileItem): String {
        return if (profile.subscriptionId.isNullOrEmpty()) {
            ""
        } else {
            val subItem = MmkvManager.decodeSubscription(profile.subscriptionId)
            subItem?.remarks ?: ""
        }
    }

    private fun shareServer(guid: String, profile: ProfileItem, position: Int, shareOptions: List<String>, startIndex: Int) {
        try {
            AlertDialog.Builder(mActivity).setItems(shareOptions.toTypedArray()) { _, i ->
                try {
                    when (i + startIndex) {
                        0 -> share2Clipboard(guid)
                        1 -> showQRCode(guid)
                        2 -> shareFullConfig(guid)
                        else -> mActivity.toastError(R.string.toast_failure)
                    }
                } catch (e: Exception) {
                    Log.e(AppConfig.TAG, "Error when sharing server", e)
                }
            }.show()
        } catch (e: Exception) {
            Log.e(AppConfig.TAG, "Error when sharing server", e)
        }
    }

    private fun showQRCode(guid: String) {
        val ivBinding = ItemQrcodeBinding.inflate(LayoutInflater.from(mActivity))
        ivBinding.ivQcode.setImageBitmap(AngConfigManager.share2QRCode(guid))
        AlertDialog.Builder(mActivity).setView(ivBinding.root).show()
    }

    private fun share2Clipboard(guid: String) {
        if (AngConfigManager.share2Clipboard(mActivity, guid) == 0) {
            mActivity.toastSuccess(R.string.toast_success)
        } else {
            mActivity.toastError(R.string.toast_failure)
        }
    }

    private fun shareFullConfig(guid: String) {
        if (AngConfigManager.shareFullContent2Clipboard(mActivity, guid) == 0) {
            mActivity.toastSuccess(R.string.toast_success)
        } else {
            mActivity.toastError(R.string.toast_failure)
        }
    }

    private fun editServer(guid: String, profile: ProfileItem) {
        val intent = Intent().putExtra("guid", guid)
            .putExtra("isRunning", isRunning)
            .putExtra("createConfigType", profile.configType.value)
        if (profile.configType == EConfigType.CUSTOM) {
            mActivity.startActivity(intent.setClass(mActivity, ServerCustomConfigActivity::class.java))
        } else {
            mActivity.startActivity(intent.setClass(mActivity, ServerActivity::class.java))
        }
    }

    private fun removeServer(guid: String, position: Int) {
        if (guid != MmkvManager.getSelectServer()) {
            AlertDialog.Builder(mActivity).setMessage(R.string.del_config_comfirm)
                .setPositiveButton(android.R.string.ok) { _, _ ->
                    removeServerSub(guid, position)
                }
                .setNegativeButton(android.R.string.cancel) { _, _ ->
                    //do nothing
                }
                .show()
        }
    }

    private fun removeServerSub(guid: String, position: Int) {
        mActivity.mainViewModel.removeServer(guid)
        notifyItemRemoved(position)
        notifyItemRangeChanged(position, mActivity.mainViewModel.serversCache.size)
    }

    private fun setSelectServer(guid: String) {
        val selected = MmkvManager.getSelectServer()
        if (guid != selected) {
            MmkvManager.setSelectServer(guid)
            if (!TextUtils.isEmpty(selected)) {
                notifyItemChanged(mActivity.mainViewModel.getPosition(selected.orEmpty()))
            }
            notifyItemChanged(mActivity.mainViewModel.getPosition(guid))
            if (isRunning) {
                V2RayServiceManager.stopVService(mActivity)
                mActivity.lifecycleScope.launch {
                    try {
                        delay(500)
                        V2RayServiceManager.startVService(mActivity)
                    } catch (e: Exception) {
                        Log.e(AppConfig.TAG, "Failed to restart V2Ray service", e)
                    }
                }
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseViewHolder {
        return when (viewType) {
            VIEW_TYPE_ITEM ->
                MainViewHolder(ItemRecyclerMainBinding.inflate(LayoutInflater.from(parent.context), parent, false))

            else ->
                FooterViewHolder(ItemRecyclerFooterBinding.inflate(LayoutInflater.from(parent.context), parent, false))
        }
    }

    override fun getItemViewType(position: Int): Int {
        return if (position == mActivity.mainViewModel.serversCache.size) {
            VIEW_TYPE_FOOTER
        } else {
            VIEW_TYPE_ITEM
        }
    }

    open class BaseViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        fun onItemSelected() {
            itemView.setBackgroundColor(Color.LTGRAY)
        }

        fun onItemClear() {
            itemView.setBackgroundColor(0)
        }
    }

    class MainViewHolder(val itemMainBinding: ItemRecyclerMainBinding) :
        BaseViewHolder(itemMainBinding.root), ItemTouchHelperViewHolder

    class FooterViewHolder(val itemFooterBinding: ItemRecyclerFooterBinding) :
        BaseViewHolder(itemFooterBinding.root)

    override fun onItemMove(fromPosition: Int, toPosition: Int): Boolean {
        mActivity.mainViewModel.swapServer(fromPosition, toPosition)
        notifyItemMoved(fromPosition, toPosition)
        return true
    }

    override fun onItemMoveCompleted() {
        // do nothing
    }

    override fun onItemDismiss(position: Int) {
    }
}
