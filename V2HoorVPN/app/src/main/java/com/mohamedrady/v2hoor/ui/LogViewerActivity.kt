package com.mohamedrady.v2hoor.ui

import android.content.Intent
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.widget.ScrollView
import android.widget.TextView
import androidx.lifecycle.lifecycleScope
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.extension.toast
import com.mohamedrady.v2hoor.extension.toastError
import com.mohamedrady.v2hoor.extension.toastSuccess
import com.mohamedrady.v2hoor.util.ErrorLogger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class LogViewerActivity : BaseActivity() {
    
    private lateinit var textView: TextView
    private lateinit var scrollView: ScrollView
    
    companion object {
        private const val TAG = "LogViewerActivity"
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Create simple layout programmatically
        scrollView = ScrollView(this)
        textView = TextView(this).apply {
            textSize = 12f
            setPadding(16, 16, 16, 16)
            setTextIsSelectable(true)
        }
        scrollView.addView(textView)
        setContentView(scrollView)
        
        title = "Error Logs"
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        
        loadLogs()
    }
    
    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menu.add(0, 1, 0, "Refresh").setShowAsAction(MenuItem.SHOW_AS_ACTION_IF_ROOM)
        menu.add(0, 2, 0, "Clear").setShowAsAction(MenuItem.SHOW_AS_ACTION_NEVER)
        menu.add(0, 3, 0, "Share").setShowAsAction(MenuItem.SHOW_AS_ACTION_IF_ROOM)
        return true
    }
    
    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                finish()
                true
            }
            1 -> {
                loadLogs()
                true
            }
            2 -> {
                clearLogs()
                true
            }
            3 -> {
                shareLogs()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }
    
    private fun loadLogs() {
        lifecycleScope.launch {
            try {
                ErrorLogger.logInfo(this@LogViewerActivity, TAG, "Loading error logs")
                
                val logs = withContext(Dispatchers.IO) {
                    ErrorLogger.getLogContent(this@LogViewerActivity)
                }
                
                textView.text = if (logs.isNotEmpty()) {
                    logs
                } else {
                    "No logs found.\nThis is good news - no errors have been recorded!"
                }
                
                // Scroll to bottom to show latest logs
                scrollView.post {
                    scrollView.fullScroll(ScrollView.FOCUS_DOWN)
                }
                
                val fileSize = ErrorLogger.getLogFileSize(this@LogViewerActivity)
                val sizeKB = fileSize / 1024
                toast("Logs loaded (${sizeKB}KB)")
                
            } catch (e: Exception) {
                ErrorLogger.logError(this@LogViewerActivity, TAG, "Failed to load logs", e)
                toastError("Failed to load logs: ${e.message}")
                textView.text = "Error loading logs: ${e.message}"
            }
        }
    }
    
    private fun clearLogs() {
        lifecycleScope.launch {
            try {
                ErrorLogger.logInfo(this@LogViewerActivity, TAG, "Clearing error logs")
                
                val success = withContext(Dispatchers.IO) {
                    ErrorLogger.clearLogs(this@LogViewerActivity)
                }
                
                if (success) {
                    toastSuccess("Logs cleared successfully")
                    textView.text = "Logs cleared.\nNew errors will be logged here."
                } else {
                    toastError("Failed to clear logs")
                }
                
            } catch (e: Exception) {
                ErrorLogger.logError(this@LogViewerActivity, TAG, "Error clearing logs", e)
                toastError("Error clearing logs: ${e.message}")
            }
        }
    }
    
    private fun shareLogs() {
        lifecycleScope.launch {
            try {
                ErrorLogger.logInfo(this@LogViewerActivity, TAG, "Sharing error logs")
                
                val logs = withContext(Dispatchers.IO) {
                    ErrorLogger.getLogContent(this@LogViewerActivity)
                }
                
                if (logs.isNotEmpty() && logs != "No log file found") {
                    val intent = Intent().apply {
                        action = Intent.ACTION_SEND
                        type = "text/plain"
                        putExtra(Intent.EXTRA_TEXT, logs)
                        putExtra(Intent.EXTRA_SUBJECT, "V2Hoor VPN Error Logs")
                    }
                    
                    startActivity(Intent.createChooser(intent, "Share Error Logs"))
                } else {
                    toast("No logs to share")
                }
                
            } catch (e: Exception) {
                ErrorLogger.logError(this@LogViewerActivity, TAG, "Failed to share logs", e)
                toastError("Failed to share logs: ${e.message}")
            }
        }
    }
}
