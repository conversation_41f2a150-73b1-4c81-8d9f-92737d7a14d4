package com.mohamedrady.v2hoor.util

import android.content.Context
import android.util.Log
import java.io.File
import java.io.FileWriter
import java.text.SimpleDateFormat
import java.util.*

object ErrorLogger {
    private const val TAG = "ErrorLogger"
    private const val LOG_FILE_NAME = "v2hoor_errors.log"
    private const val MAX_LOG_SIZE = 1024 * 1024 // 1MB
    
    fun logError(context: Context, tag: String, message: String, throwable: Throwable? = null) {
        // Log to Android Log
        if (throwable != null) {
            Log.e(tag, message, throwable)
        } else {
            Log.e(tag, message)
        }
        
        // Log to file
        try {
            val logFile = File(context.filesDir, LOG_FILE_NAME)
            
            // Check file size and rotate if needed
            if (logFile.exists() && logFile.length() > MAX_LOG_SIZE) {
                rotateLogFile(logFile)
            }
            
            val timestamp = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault()).format(Date())
            val logEntry = buildString {
                append("[$timestamp] ERROR [$tag] $message")
                if (throwable != null) {
                    append("\n")
                    append("Exception: ${throwable.javaClass.simpleName}")
                    append("\nMessage: ${throwable.message}")
                    append("\nStackTrace: ${throwable.stackTraceToString()}")
                }
                append("\n")
            }
            
            FileWriter(logFile, true).use { writer ->
                writer.write(logEntry)
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to write error to log file", e)
        }
    }
    
    fun logInfo(context: Context, tag: String, message: String) {
        Log.i(tag, message)
        
        try {
            val logFile = File(context.filesDir, LOG_FILE_NAME)
            
            if (logFile.exists() && logFile.length() > MAX_LOG_SIZE) {
                rotateLogFile(logFile)
            }
            
            val timestamp = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault()).format(Date())
            val logEntry = "[$timestamp] INFO [$tag] $message\n"
            
            FileWriter(logFile, true).use { writer ->
                writer.write(logEntry)
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to write info to log file", e)
        }
    }
    
    fun logDebug(context: Context, tag: String, message: String) {
        Log.d(tag, message)
        
        try {
            val logFile = File(context.filesDir, LOG_FILE_NAME)
            
            if (logFile.exists() && logFile.length() > MAX_LOG_SIZE) {
                rotateLogFile(logFile)
            }
            
            val timestamp = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault()).format(Date())
            val logEntry = "[$timestamp] DEBUG [$tag] $message\n"
            
            FileWriter(logFile, true).use { writer ->
                writer.write(logEntry)
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to write debug to log file", e)
        }
    }
    
    fun logWarning(context: Context, tag: String, message: String) {
        Log.w(tag, message)
        
        try {
            val logFile = File(context.filesDir, LOG_FILE_NAME)
            
            if (logFile.exists() && logFile.length() > MAX_LOG_SIZE) {
                rotateLogFile(logFile)
            }
            
            val timestamp = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault()).format(Date())
            val logEntry = "[$timestamp] WARN [$tag] $message\n"
            
            FileWriter(logFile, true).use { writer ->
                writer.write(logEntry)
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to write warning to log file", e)
        }
    }
    
    private fun rotateLogFile(logFile: File) {
        try {
            val backupFile = File(logFile.parent, "${LOG_FILE_NAME}.old")
            if (backupFile.exists()) {
                backupFile.delete()
            }
            logFile.renameTo(backupFile)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to rotate log file", e)
        }
    }
    
    fun getLogContent(context: Context): String {
        return try {
            val logFile = File(context.filesDir, LOG_FILE_NAME)
            if (logFile.exists()) {
                logFile.readText()
            } else {
                "No log file found"
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to read log file", e)
            "Error reading log file: ${e.message}"
        }
    }
    
    fun clearLogs(context: Context): Boolean {
        return try {
            val logFile = File(context.filesDir, LOG_FILE_NAME)
            val backupFile = File(logFile.parent, "${LOG_FILE_NAME}.old")
            
            var success = true
            if (logFile.exists()) {
                success = logFile.delete()
            }
            if (backupFile.exists()) {
                success = success && backupFile.delete()
            }
            
            success
        } catch (e: Exception) {
            Log.e(TAG, "Failed to clear log files", e)
            false
        }
    }
    
    fun getLogFileSize(context: Context): Long {
        return try {
            val logFile = File(context.filesDir, LOG_FILE_NAME)
            if (logFile.exists()) logFile.length() else 0
        } catch (e: Exception) {
            0
        }
    }
}
