package com.mohamedrady.v2hoor.util

import android.app.Activity
import android.app.Application
import android.content.Context
import android.os.Bundle
import android.util.Log
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ProcessLifecycleOwner
import java.io.File
import java.io.FileWriter
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.ConcurrentLinkedQueue

class RuntimeErrorMonitor private constructor() : Application.ActivityLifecycleCallbacks, DefaultLifecycleObserver {
    
    companion object {
        private const val TAG = "RuntimeErrorMonitor"
        private const val RUNTIME_LOG_FILE = "runtime_errors.log"
        private const val MAX_LOG_ENTRIES = 1000
        
        @Volatile
        private var instance: RuntimeErrorMonitor? = null
        
        fun getInstance(): RuntimeErrorMonitor {
            return instance ?: synchronized(this) {
                instance ?: RuntimeErrorMonitor().also { instance = it }
            }
        }
        
        fun init(application: Application) {
            val monitor = getInstance()
            monitor.context = application.applicationContext
            application.registerActivityLifecycleCallbacks(monitor)
            ProcessLifecycleOwner.get().lifecycle.addObserver(monitor)
            Log.i(TAG, "RuntimeErrorMonitor initialized")
        }
        
        fun logError(context: Context, activity: String, error: String, exception: Throwable? = null) {
            getInstance().recordError(context, activity, error, exception)
        }
        
        fun logWarning(context: Context, activity: String, warning: String) {
            getInstance().recordWarning(context, activity, warning)
        }
        
        fun logInfo(context: Context, activity: String, info: String) {
            getInstance().recordInfo(context, activity, info)
        }
        
        fun getRecentErrors(context: Context): List<String> {
            return getInstance().getRecentErrorsList(context)
        }
    }
    
    private var context: Context? = null
    private val errorQueue = ConcurrentLinkedQueue<String>()
    private var currentActivity: String = "Unknown"
    
    private fun recordError(context: Context, activity: String, error: String, exception: Throwable?) {
        val timestamp = SimpleDateFormat("HH:mm:ss.SSS", Locale.getDefault()).format(Date())
        val logEntry = buildString {
            append("[$timestamp] ERROR [$activity] $error")
            if (exception != null) {
                append("\n  Exception: ${exception.javaClass.simpleName}")
                append("\n  Message: ${exception.message}")
                append("\n  Stack: ${exception.stackTrace.take(3).joinToString(" -> ") { "${it.className}.${it.methodName}:${it.lineNumber}" }}")
            }
        }
        
        Log.e(TAG, logEntry)
        addToQueue(logEntry)
        writeToFile(context, logEntry)
    }
    
    private fun recordWarning(context: Context, activity: String, warning: String) {
        val timestamp = SimpleDateFormat("HH:mm:ss.SSS", Locale.getDefault()).format(Date())
        val logEntry = "[$timestamp] WARN [$activity] $warning"
        
        Log.w(TAG, logEntry)
        addToQueue(logEntry)
        writeToFile(context, logEntry)
    }
    
    private fun recordInfo(context: Context, activity: String, info: String) {
        val timestamp = SimpleDateFormat("HH:mm:ss.SSS", Locale.getDefault()).format(Date())
        val logEntry = "[$timestamp] INFO [$activity] $info"
        
        Log.i(TAG, logEntry)
        addToQueue(logEntry)
        writeToFile(context, logEntry)
    }
    
    private fun addToQueue(entry: String) {
        errorQueue.offer(entry)
        while (errorQueue.size > MAX_LOG_ENTRIES) {
            errorQueue.poll()
        }
    }
    
    private fun writeToFile(context: Context, entry: String) {
        try {
            val logFile = File(context.filesDir, RUNTIME_LOG_FILE)
            
            // Rotate file if too large
            if (logFile.exists() && logFile.length() > 512 * 1024) { // 512KB
                val backupFile = File(context.filesDir, "$RUNTIME_LOG_FILE.old")
                if (backupFile.exists()) backupFile.delete()
                logFile.renameTo(backupFile)
            }
            
            FileWriter(logFile, true).use { writer ->
                writer.write("$entry\n")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to write to runtime log file", e)
        }
    }
    
    private fun getRecentErrorsList(context: Context): List<String> {
        return try {
            val logFile = File(context.filesDir, RUNTIME_LOG_FILE)
            if (logFile.exists()) {
                logFile.readLines().takeLast(50) // Last 50 entries
            } else {
                emptyList()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to read runtime log file", e)
            emptyList()
        }
    }
    
    // Activity Lifecycle Callbacks
    override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
        currentActivity = activity.javaClass.simpleName
        recordInfo(activity, currentActivity, "Activity created")
    }
    
    override fun onActivityStarted(activity: Activity) {
        currentActivity = activity.javaClass.simpleName
        recordInfo(activity, currentActivity, "Activity started")
    }
    
    override fun onActivityResumed(activity: Activity) {
        currentActivity = activity.javaClass.simpleName
        recordInfo(activity, currentActivity, "Activity resumed")
    }
    
    override fun onActivityPaused(activity: Activity) {
        recordInfo(activity, currentActivity, "Activity paused")
    }
    
    override fun onActivityStopped(activity: Activity) {
        recordInfo(activity, currentActivity, "Activity stopped")
    }
    
    override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {
        recordInfo(activity, currentActivity, "Activity save instance state")
    }
    
    override fun onActivityDestroyed(activity: Activity) {
        recordInfo(activity, currentActivity, "Activity destroyed")
    }
    
    // Process Lifecycle Observer
    override fun onStart(owner: LifecycleOwner) {
        context?.let { recordInfo(it, "App", "App moved to foreground") }
    }
    
    override fun onStop(owner: LifecycleOwner) {
        context?.let { recordInfo(it, "App", "App moved to background") }
    }
    
    fun getCurrentActivity(): String = currentActivity
    
    fun getQueueSize(): Int = errorQueue.size
    
    fun clearLogs(context: Context): Boolean {
        return try {
            errorQueue.clear()
            val logFile = File(context.filesDir, RUNTIME_LOG_FILE)
            val backupFile = File(context.filesDir, "$RUNTIME_LOG_FILE.old")
            
            var success = true
            if (logFile.exists()) success = logFile.delete()
            if (backupFile.exists()) success = success && backupFile.delete()
            
            recordInfo(context, "Monitor", "Logs cleared")
            success
        } catch (e: Exception) {
            Log.e(TAG, "Failed to clear runtime logs", e)
            false
        }
    }
    
    fun exportLogs(context: Context): String {
        return try {
            val logFile = File(context.filesDir, RUNTIME_LOG_FILE)
            if (logFile.exists()) {
                logFile.readText()
            } else {
                "No runtime logs found"
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to export runtime logs", e)
            "Error exporting logs: ${e.message}"
        }
    }
}
