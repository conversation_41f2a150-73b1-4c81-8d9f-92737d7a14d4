package com.mohamedrady.v2hoor.util

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.res.Configuration
import android.os.Build
import androidx.appcompat.app.AlertDialog
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.handler.MmkvManager
import java.util.*

/**
 * Language utility for V2Hoor VPN
 * Handles language switching between English and Arabic (Egypt)
 */
object LanguageUtil {
    
    // Language codes - Only English and Arabic (Egypt) supported
    const val LANGUAGE_ENGLISH = "en"
    const val LANGUAGE_ARABIC_EGYPT = "ar-rEG"

    // Language preferences key
    private const val PREF_LANGUAGE = "pref_language"
    
    /**
     * Get the currently selected language
     * Defaults to Arabic (Egypt) if no language is set
     */
    fun getCurrentLanguage(): String {
        return try {
            MmkvManager.decodeSettingsString(PREF_LANGUAGE) ?: LANGUAGE_ARABIC_EGYPT
        } catch (e: Exception) {
            // MMKV not initialized yet, return default
            LANGUAGE_ARABIC_EGYPT
        }
    }

    /**
     * Set the app language
     */
    fun setLanguage(languageCode: String) {
        try {
            MmkvManager.encodeSettings(PREF_LANGUAGE, languageCode)
        } catch (e: Exception) {
            // MMKV not initialized yet, ignore
        }
    }
    
    /**
     * Apply language configuration to context
     * Only supports English and Arabic (Egypt)
     */
    fun applyLanguage(context: Context): Context {
        val languageCode = getCurrentLanguage()

        val locale = when (languageCode) {
            LANGUAGE_ARABIC_EGYPT -> Locale("ar", "EG")
            LANGUAGE_ENGLISH -> Locale("en")
            else -> Locale("ar", "EG") // Default to Arabic (Egypt)
        }

        Locale.setDefault(locale)

        val configuration = Configuration(context.resources.configuration)
        configuration.setLocale(locale)

        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            context.createConfigurationContext(configuration)
        } else {
            @Suppress("DEPRECATION")
            context.resources.updateConfiguration(configuration, context.resources.displayMetrics)
            context
        }
    }
    
    /**
     * Get display name for language code
     * Only supports English and Arabic (Egypt)
     */
    fun getLanguageDisplayName(context: Context, languageCode: String): String {
        return when (languageCode) {
            LANGUAGE_ENGLISH -> context.getString(R.string.language_english)
            LANGUAGE_ARABIC_EGYPT -> context.getString(R.string.language_arabic_egypt)
            else -> context.getString(R.string.language_arabic_egypt) // Default to Arabic (Egypt)
        }
    }
    
    /**
     * Show language selection dialog
     * Only shows English and Arabic (Egypt) - all other languages have been removed
     */
    fun showLanguageSelectionDialog(activity: Activity) {
        val languages = arrayOf(
            LANGUAGE_ENGLISH,
            LANGUAGE_ARABIC_EGYPT
        )

        val languageNames = languages.map {
            getLanguageDisplayName(activity, it)
        }.toTypedArray()

        val currentLanguage = getCurrentLanguage()
        val selectedIndex = languages.indexOf(currentLanguage).takeIf { it >= 0 } ?: 1 // Default to Arabic

        MaterialAlertDialogBuilder(activity)
            .setTitle(R.string.title_language)
            .setSingleChoiceItems(languageNames, selectedIndex) { dialog, which ->
                val selectedLanguage = languages[which]
                if (selectedLanguage != currentLanguage) {
                    setLanguage(selectedLanguage)

                    // Show restart dialog
                    showRestartDialog(activity)
                }
                dialog.dismiss()
            }
            .setNegativeButton(android.R.string.cancel, null)
            .show()
    }
    
    /**
     * Show restart dialog after language change
     */
    fun showRestartDialog(activity: Activity) {
        MaterialAlertDialogBuilder(activity)
            .setTitle(R.string.restart_required_title)
            .setMessage(R.string.restart_required_message)
            .setPositiveButton(R.string.restart_now) { _, _ ->
                restartApp(activity)
            }
            .setNegativeButton(R.string.restart_later, null)
            .setCancelable(false)
            .show()
    }
    
    /**
     * Restart the application
     */
    private fun restartApp(activity: Activity) {
        val intent = activity.packageManager.getLaunchIntentForPackage(activity.packageName)
        intent?.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_NEW_TASK)
        activity.startActivity(intent)
        activity.finish()
        
        // Force kill the process to ensure clean restart
        android.os.Process.killProcess(android.os.Process.myPid())
    }
    
    /**
     * Check if current language is RTL (Right-to-Left)
     * Only Arabic (Egypt) is RTL in this app
     */
    fun isRTL(): Boolean {
        val currentLanguage = getCurrentLanguage()
        return currentLanguage == LANGUAGE_ARABIC_EGYPT
    }
    
    /**
     * Get text direction for layouts
     */
    fun getTextDirection(): Int {
        return if (isRTL()) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
                android.view.View.TEXT_DIRECTION_RTL
            } else {
                android.view.View.TEXT_DIRECTION_LTR
            }
        } else {
            android.view.View.TEXT_DIRECTION_LTR
        }
    }
    
    /**
     * Apply RTL support to activity
     */
    fun applyRTLSupport(activity: Activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
            if (isRTL()) {
                activity.window.decorView.layoutDirection = android.view.View.LAYOUT_DIRECTION_RTL
            } else {
                activity.window.decorView.layoutDirection = android.view.View.LAYOUT_DIRECTION_LTR
            }
        }
    }
}
