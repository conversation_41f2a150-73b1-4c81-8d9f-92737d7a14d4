package com.mohamedrady.v2hoor

import android.content.Context
import androidx.multidex.MultiDexApplication
import androidx.work.Configuration
import androidx.work.WorkManager
import com.tencent.mmkv.MMKV
import com.mohamedrady.v2hoor.AppConfig.ANG_PACKAGE
import com.mohamedrady.v2hoor.handler.SettingsManager
import com.mohamedrady.v2hoor.util.CrashHandler
import com.mohamedrady.v2hoor.util.LanguageUtil

class AngApplication : MultiDexApplication() {
    companion object {
        lateinit var application: AngApplication
    }

    /**
     * Attaches the base context to the application.
     * @param base The base context.
     */
    override fun attachBaseContext(base: Context?) {
        val context = base?.let { LanguageUtil.applyLanguage(it) } ?: base
        super.attachBaseContext(context)
        application = this
    }

    private val workManagerConfiguration: Configuration = Configuration.Builder()
        .setDefaultProcessName("${ANG_PACKAGE}:bg")
        .build()

    /**
     * Initializes the application.
     */
    override fun onCreate() {
        super.onCreate()

        // Initialize crash handler first
        CrashHandler.init(this)

        MMKV.initialize(this)

        SettingsManager.setNightMode()
        // Initialize WorkManager with the custom configuration
        WorkManager.initialize(this, workManagerConfiguration)

        SettingsManager.initRoutingRulesets(this)

        es.dmoral.toasty.Toasty.Config.getInstance()
            .setGravity(android.view.Gravity.BOTTOM, 0, 200)
            .apply()
    }
}
