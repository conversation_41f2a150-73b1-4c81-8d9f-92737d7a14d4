package com.mohamedrady.v2hoor.ui

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.BaseAdapter
import android.widget.TextView
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.dto.ServersCache

class ServerSpinnerAdapter(
    private val context: Context,
    private var servers: List<ServersCache>
) : BaseAdapter() {

    private val inflater: LayoutInflater = LayoutInflater.from(context)

    override fun getCount(): Int = servers.size

    override fun getItem(position: Int): ServersCache = servers[position]

    override fun getItemId(position: Int): Long = position.toLong()

    override fun getView(position: Int, convertView: View?, parent: ViewGroup?): View {
        val view = convertView ?: inflater.inflate(R.layout.spinner_server_selected, parent, false)
        val server = getItem(position)

        val tvServerName = view.findViewById<TextView>(R.id.tv_server_name)
        tvServerName.text = if (server.profile.remarks.isNotEmpty()) {
            server.profile.remarks
        } else {
            "${server.profile.server}:${server.profile.serverPort}"
        }

        return view
    }

    override fun getDropDownView(position: Int, convertView: View?, parent: ViewGroup?): View {
        val view = convertView ?: inflater.inflate(R.layout.spinner_server_item, parent, false)
        val server = getItem(position)

        val tvServerName = view.findViewById<TextView>(R.id.tv_server_name)
        val tvServerType = view.findViewById<TextView>(R.id.tv_server_type)

        tvServerName.text = if (server.profile.remarks.isNotEmpty()) {
            server.profile.remarks
        } else {
            "${server.profile.server}:${server.profile.serverPort}"
        }
        tvServerType.text = server.profile.configType.name

        return view
    }

    fun updateServers(newServers: List<ServersCache>) {
        servers = newServers
        notifyDataSetChanged()
    }
}
