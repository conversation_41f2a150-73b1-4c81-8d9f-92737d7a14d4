/******************************************************************************
 *                                                                            *
 * Copyright (C) 2021 by <PERSON><PERSON><PERSON><PERSON><PERSON> <contact-sage<PERSON><EMAIL>>             *
 * Copyright (C) 2021 by Max Lv <<EMAIL>>                          *
 * Copyright (C) 2021 by Mygod Studio <<EMAIL>>  *
 *                                                                            *
 * This program is free software: you can redistribute it and/or modify       *
 * it under the terms of the GNU General Public License as published by       *
 * the Free Software Foundation, either version 3 of the License, or          *
 *  (at your option) any later version.                                       *
 *                                                                            *
 * This program is distributed in the hope that it will be useful,            *
 * but WITHOUT ANY WARRANTY; without even the implied warranty of             *
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the              *
 * GNU General Public License for more details.                               *
 *                                                                            *
 * You should have received a copy of the GNU General Public License          *
 * along with this program. If not, see <http://www.gnu.org/licenses/>.       *
 *                                                                            *
 ******************************************************************************/

package com.mohamedrady.v2hoor.plugin

import android.content.pm.ResolveInfo

class NativePlugin(resolveInfo: ResolveInfo) : ResolvedPlugin(resolveInfo) {
    init {
        check(resolveInfo.providerInfo != null)
    }

    override val componentInfo get() = resolveInfo.providerInfo!!
}
