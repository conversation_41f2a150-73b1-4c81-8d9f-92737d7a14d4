package com.mohamedrady.v2hoor.ui

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Bundle
import android.util.Log
import android.view.Menu
import android.view.MenuItem
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AlertDialog
import androidx.core.content.ContextCompat
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.tabs.TabLayout
import com.mohamedrady.v2hoor.AppConfig
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.databinding.ActivityServersManagementBinding
import com.mohamedrady.v2hoor.extension.toast
import com.mohamedrady.v2hoor.extension.toastError
import com.mohamedrady.v2hoor.extension.toastSuccess
import com.mohamedrady.v2hoor.handler.AngConfigManager
import com.mohamedrady.v2hoor.handler.MmkvManager
import com.mohamedrady.v2hoor.helper.SimpleItemTouchHelperCallback
import com.mohamedrady.v2hoor.service.V2RayServiceManager
import com.mohamedrady.v2hoor.util.Utils
import com.mohamedrady.v2hoor.viewmodel.MainViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class ServersManagementActivity : BaseActivity() {
    private val binding by lazy { ActivityServersManagementBinding.inflate(layoutInflater) }

    lateinit var mainViewModel: MainViewModel
    private lateinit var adapter: ServersManagementRecyclerAdapter
    private val requestPermissionLauncher = registerForActivityResult(ActivityResultContracts.RequestPermission()) { isGranted ->
        if (isGranted) {
            importQRcode()
        } else {
            toastError(R.string.toast_permission_denied)
        }
    }

    private val scanQRCodeForConfig = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {
        if (it.resultCode == RESULT_OK) {
            importBatchConfig(it.data?.getStringExtra("SCAN_RESULT"))
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        title = getString(R.string.title_servers_management)
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)

        mainViewModel = ViewModelProvider(this)[MainViewModel::class.java]
        adapter = ServersManagementRecyclerAdapter(this)
        setupRecyclerView()
        setupFab()
        setupTabs()
        observeViewModel()
    }

    private fun setupRecyclerView() {
        binding.recyclerView.setHasFixedSize(true)
        binding.recyclerView.layoutManager = LinearLayoutManager(this)
        addCustomDividerToRecyclerView(binding.recyclerView, this, R.drawable.custom_divider)
        binding.recyclerView.adapter = adapter

        val callback = SimpleItemTouchHelperCallback(adapter)
        val touchHelper = ItemTouchHelper(callback)
        touchHelper.attachToRecyclerView(binding.recyclerView)
    }

    private fun setupFab() {
        binding.fab.setOnClickListener {
            if (mainViewModel.isRunning.value == true) {
                V2RayServiceManager.stopVService(this)
            } else {
                V2RayServiceManager.startVService(this)
            }
        }
    }

    private fun setupTabs() {
        binding.tabGroup.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                val selectId = tab?.tag.toString()
                if (selectId != mainViewModel.subscriptionId) {
                    mainViewModel.subscriptionIdChanged(selectId)
                }
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {}
            override fun onTabReselected(tab: TabLayout.Tab?) {}
        })
    }

    private fun observeViewModel() {
        mainViewModel.updateListAction.observe(this) { index ->
            if (index >= 0) {
                adapter.notifyItemChanged(index)
            } else {
                adapter.notifyDataSetChanged()
            }
        }

        mainViewModel.updateTestResultAction.observe(this) { guid ->
            adapter.notifyItemChanged(mainViewModel.getPosition(guid))
        }

        mainViewModel.isRunning.observe(this) { isRunning ->
            adapter.isRunning = isRunning
            if (isRunning) {
                binding.fab.setImageResource(R.drawable.ic_stop_24dp)
            } else {
                binding.fab.setImageResource(R.drawable.ic_play_24dp)
            }
        }
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.menu_servers_management, menu)
        return super.onCreateOptionsMenu(menu)
    }

    override fun onOptionsItemSelected(item: MenuItem) = when (item.itemId) {
        android.R.id.home -> {
            finish()
            true
        }
        R.id.import_qrcode -> {
            importQRcode()
            true
        }
        R.id.import_clipboard -> {
            importClipboard()
            true
        }
        R.id.import_file -> {
            importConfigLocal()
            true
        }
        else -> super.onOptionsItemSelected(item)
    }

    /**
     * Import config from QR code
     */
    private fun importQRcode(): Boolean {
        val permission = Manifest.permission.CAMERA
        if (ContextCompat.checkSelfPermission(this, permission) == PackageManager.PERMISSION_GRANTED) {
            scanQRCodeForConfig.launch(Intent(this, ScannerActivity::class.java))
        } else {
            requestPermissionLauncher.launch(permission)
        }
        return true
    }

    /**
     * Import config from clipboard
     */
    private fun importClipboard(): Boolean {
        try {
            val clipboard = Utils.getClipboard(this)
            if (clipboard.isBlank()) {
                toastError(R.string.toast_clipboard_empty)
                return false
            }

            // Show confirmation dialog before importing
            AlertDialog.Builder(this)
                .setTitle(R.string.import_config_from_clipboard)
                .setMessage(getString(R.string.import_config_from_clipboard) + "?")
                .setPositiveButton(android.R.string.ok) { _, _ ->
                    importBatchConfig(clipboard)
                }
                .setNegativeButton(android.R.string.cancel, null)
                .show()

        } catch (e: Exception) {
            Log.e(AppConfig.TAG, "Failed to import config from clipboard", e)
            toastError(R.string.toast_failure)
            return false
        }
        return true
    }

    /**
     * Import config from local file
     */
    private fun importConfigLocal() {
        try {
            val intent = Intent(Intent.ACTION_GET_CONTENT).apply {
                type = "*/*"
                addCategory(Intent.CATEGORY_OPENABLE)
            }
            startActivityForResult(intent, 1)
        } catch (e: Exception) {
            Log.e(AppConfig.TAG, "Failed to open file picker", e)
            toastError(R.string.toast_failure)
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == 1 && resultCode == RESULT_OK) {
            data?.data?.let { uri ->
                try {
                    val content = contentResolver.openInputStream(uri)?.bufferedReader()?.use { it.readText() }
                    if (!content.isNullOrBlank()) {
                        importBatchConfig(content)
                    } else {
                        toastError(R.string.toast_failure)
                    }
                } catch (e: Exception) {
                    Log.e(AppConfig.TAG, "Failed to read file", e)
                    toastError(R.string.toast_failure)
                }
            }
        }
    }

    private fun importBatchConfig(server: String?) {
        if (server.isNullOrBlank()) {
            toastError(R.string.toast_clipboard_empty)
            return
        }

        binding.pbWaiting.visibility = android.view.View.VISIBLE

        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val (count, countSub) = AngConfigManager.importBatchConfig(server, mainViewModel.subscriptionId, true)
                delay(500L)
                withContext(Dispatchers.Main) {
                    when {
                        count > 0 -> {
                            toast(getString(R.string.title_import_config_count, count))
                            mainViewModel.reloadServerList()
                        }
                        countSub > 0 -> {
                            toast(R.string.import_subscription_success)
                            mainViewModel.reloadServerList()
                        }
                        else -> toastError(R.string.toast_failure)
                    }
                    binding.pbWaiting.visibility = android.view.View.INVISIBLE
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    Log.e(AppConfig.TAG, "Failed to import batch config", e)
                    toastError(R.string.toast_failure)
                    binding.pbWaiting.visibility = android.view.View.INVISIBLE
                }
            }
        }
    }

    private fun initGroupTab() {
        binding.tabGroup.removeAllTabs()
        val subscriptions = MmkvManager.decodeSubscriptions()
        subscriptions.forEach { item ->
            val tab = binding.tabGroup.newTab()
            tab.text = item.second.remarks.ifEmpty { getString(R.string.filter_config_all) }
            tab.tag = item.first
            binding.tabGroup.addTab(tab)
        }

        val selectTab = binding.tabGroup.getTabAt(subscriptions.indexOfFirst { it.first == mainViewModel.subscriptionId })
        selectTab?.select()
    }

    override fun onResume() {
        super.onResume()
        initGroupTab()
        mainViewModel.reloadServerList()
    }
}
