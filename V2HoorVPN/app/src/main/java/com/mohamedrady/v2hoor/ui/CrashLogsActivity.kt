package com.mohamedrady.v2hoor.ui

import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.Menu
import android.view.MenuItem
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.mohamedrady.v2hoor.AppConfig
import com.mohamedrady.v2hoor.R
import com.mohamedrady.v2hoor.databinding.ActivityCrashLogsBinding
import com.mohamedrady.v2hoor.extension.toast
import com.mohamedrady.v2hoor.extension.toastError
import com.mohamedrady.v2hoor.extension.toastSuccess
import com.mohamedrady.v2hoor.util.CrashHandler
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.text.SimpleDateFormat
import java.util.*

class CrashLogsActivity : BaseActivity() {
    
    private val binding by lazy { ActivityCrashLogsBinding.inflate(layoutInflater) }
    private lateinit var adapter: CrashLogsAdapter
    
    companion object {
        private const val TAG = "CrashLogsActivity"
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        
        title = "Crash Logs"
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        
        setupRecyclerView()
        loadCrashLogs()
    }
    
    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.menu_crash_logs, menu)
        return true
    }
    
    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                finish()
                true
            }
            R.id.action_refresh -> {
                loadCrashLogs()
                true
            }
            R.id.action_clear_all -> {
                clearAllLogs()
                true
            }
            R.id.action_share_latest -> {
                shareLatestLog()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }
    
    private fun setupRecyclerView() {
        adapter = CrashLogsAdapter { file ->
            shareLogFile(file)
        }
        binding.recyclerView.layoutManager = LinearLayoutManager(this)
        binding.recyclerView.adapter = adapter
    }
    
    private fun loadCrashLogs() {
        lifecycleScope.launch {
            try {
                Log.d(TAG, "Loading crash logs...")
                binding.progressBar.visibility = android.view.View.VISIBLE
                
                val logs = withContext(Dispatchers.IO) {
                    CrashHandler.getCrashLogs(this@CrashLogsActivity)
                }
                
                Log.d(TAG, "Found ${logs.size} crash logs")
                adapter.updateLogs(logs)
                
                if (logs.isEmpty()) {
                    binding.emptyView.visibility = android.view.View.VISIBLE
                    binding.recyclerView.visibility = android.view.View.GONE
                } else {
                    binding.emptyView.visibility = android.view.View.GONE
                    binding.recyclerView.visibility = android.view.View.VISIBLE
                }
                
                binding.progressBar.visibility = android.view.View.GONE
                
            } catch (e: Exception) {
                Log.e(TAG, "Failed to load crash logs", e)
                toastError("Failed to load crash logs: ${e.message}")
                binding.progressBar.visibility = android.view.View.GONE
            }
        }
    }
    
    private fun clearAllLogs() {
        lifecycleScope.launch {
            try {
                Log.d(TAG, "Clearing all crash logs...")
                
                val success = withContext(Dispatchers.IO) {
                    CrashHandler.clearCrashLogs(this@CrashLogsActivity)
                }
                
                if (success) {
                    Log.i(TAG, "All crash logs cleared successfully")
                    toastSuccess("All crash logs cleared")
                    loadCrashLogs()
                } else {
                    Log.w(TAG, "Failed to clear crash logs")
                    toastError("Failed to clear crash logs")
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "Error clearing crash logs", e)
                toastError("Error clearing logs: ${e.message}")
            }
        }
    }
    
    private fun shareLatestLog() {
        lifecycleScope.launch {
            try {
                Log.d(TAG, "Sharing latest crash log...")
                
                val latestLog = withContext(Dispatchers.IO) {
                    CrashHandler.getLatestCrashLog(this@CrashLogsActivity)
                }
                
                if (latestLog != null) {
                    shareLogFile(latestLog)
                } else {
                    Log.w(TAG, "No crash logs found to share")
                    toast("No crash logs found")
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "Error sharing latest log", e)
                toastError("Error sharing log: ${e.message}")
            }
        }
    }
    
    private fun shareLogFile(file: File) {
        try {
            Log.d(TAG, "Sharing log file: ${file.name}")
            
            val content = file.readText()
            val intent = Intent().apply {
                action = Intent.ACTION_SEND
                type = "text/plain"
                putExtra(Intent.EXTRA_TEXT, content)
                putExtra(Intent.EXTRA_SUBJECT, "V2Hoor VPN Crash Log - ${file.name}")
            }
            
            startActivity(Intent.createChooser(intent, "Share Crash Log"))
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to share log file", e)
            toastError("Failed to share log: ${e.message}")
        }
    }
}

class CrashLogsAdapter(
    private val onItemClick: (File) -> Unit
) : androidx.recyclerview.widget.RecyclerView.Adapter<CrashLogsAdapter.ViewHolder>() {
    
    private var logs = listOf<File>()
    
    fun updateLogs(newLogs: List<File>) {
        logs = newLogs
        notifyDataSetChanged()
    }
    
    override fun onCreateViewHolder(parent: android.view.ViewGroup, viewType: Int): ViewHolder {
        val view = android.view.LayoutInflater.from(parent.context)
            .inflate(android.R.layout.simple_list_item_2, parent, false)
        return ViewHolder(view)
    }
    
    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val file = logs[position]
        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        
        holder.title.text = file.name
        holder.subtitle.text = "Size: ${file.length() / 1024} KB | Modified: ${dateFormat.format(Date(file.lastModified()))}"
        
        holder.itemView.setOnClickListener {
            onItemClick(file)
        }
    }
    
    override fun getItemCount() = logs.size
    
    class ViewHolder(view: android.view.View) : androidx.recyclerview.widget.RecyclerView.ViewHolder(view) {
        val title: android.widget.TextView = view.findViewById(android.R.id.text1)
        val subtitle: android.widget.TextView = view.findViewById(android.R.id.text2)
    }
}
