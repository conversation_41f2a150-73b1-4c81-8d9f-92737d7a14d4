[{"remarks": "绕过bittorrent", "outboundTag": "direct", "protocol": ["bittorrent"]}, {"remarks": "Google cn", "outboundTag": "proxy", "domain": ["domain:googleapis.cn", "domain:gstatic.com"]}, {"remarks": "阻断udp443", "outboundTag": "block", "port": "443", "network": "udp"}, {"remarks": "绕过局域网IP", "outboundTag": "direct", "ip": ["geoip:private"]}, {"remarks": "绕过局域网域名", "outboundTag": "direct", "domain": ["geosite:private"]}, {"remarks": "代理海外公共DNSIP", "outboundTag": "proxy", "ip": ["*******", "*******", "2606:4700:4700::1111", "2606:4700:4700::1001", "*******", "*******", "2606:4700:4700::1112", "2606:4700:4700::1002", "*******", "*******", "2606:4700:4700::1113", "2606:4700:4700::1003", "*******", "*******", "2001:4860:4860::8888", "2001:4860:4860::8844", "************", "************", "2a10:50c0::ad1:ff", "2a10:50c0::ad2:ff", "************", "************", "2a10:50c0::bad1:ff", "2a10:50c0::bad2:ff", "************0", "************1", "2a10:50c0::1:ff", "2a10:50c0::2:ff", "**************", "**************", "2620:119:35::35", "2620:119:53::53", "**************", "**************", "2620:119:35::123", "2620:119:53::123", "*******", "***************", "2620:fe::9", "2620:fe::fe", "********", "**************", "2620:fe::11", "2620:fe::fe:11", "********", "**************", "2620:fe::10", "2620:fe::fe:10", "*********", "*********", "2a02:6b8::feed:0ff", "2a02:6b8:0:1::feed:0ff", "**********", "*********", "2a02:6b8::feed:bad", "2a02:6b8:0:1::feed:bad", "*********", "*********", "2a02:6b8::feed:a11", "2a02:6b8:0:1::feed:a11"]}, {"remarks": "代理海外公共DNS域名", "outboundTag": "proxy", "domain": ["domain:cloudflare-dns.com", "domain:one.one.one.one", "domain:dns.google", "domain:adguard-dns.com", "domain:opendns.com", "domain:umbrella.com", "domain:quad9.net", "domain:yandex.net"]}, {"remarks": "代理IP", "outboundTag": "proxy", "ip": ["geoip:facebook", "geoip:fastly", "geoip:google", "geoip:netflix", "geoip:telegram", "geoip:twitter"]}, {"remarks": "代理GFW", "outboundTag": "proxy", "domain": ["geosite:gfw", "geosite:greatfire"]}, {"remarks": "最终直连", "port": "0-65535", "outboundTag": "direct"}]