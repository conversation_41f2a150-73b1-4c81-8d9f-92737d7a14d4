<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <item
        android:id="@+id/add_rule"
        android:icon="@drawable/ic_add_24dp"
        android:title="@string/routing_settings_add_rule"
        app:showAsAction="ifRoom" />
    <item
        android:id="@+id/import_predefined_rulesets"
        android:title="@string/routing_settings_import_predefined_rulesets"
        app:showAsAction="never" />
    <item
        android:id="@+id/import_rulesets_from_clipboard"
        android:title="@string/routing_settings_import_rulesets_from_clipboard"
        app:showAsAction="never" />
    <item
        android:id="@+id/import_rulesets_from_qrcode"
        android:title="@string/routing_settings_import_rulesets_from_qrcode"
        app:showAsAction="never" />
    <item
        android:id="@+id/export_rulesets_to_clipboard"
        android:title="@string/routing_settings_export_rulesets_to_clipboard"
        app:showAsAction="never" />

</menu>