<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string-array name="securitys" translatable="false">
        <item>chacha20-poly1305</item>
        <item>aes-128-gcm</item>
        <item>auto</item>
        <item>none</item>
        <item>zero</item>
    </string-array>
    <string-array name="ss_securitys" translatable="false">
        <item>aes-256-gcm</item>
        <item>aes-128-gcm</item>
        <item>chacha20-poly1305</item>
        <item>chacha20-ietf-poly1305</item>
        <item>xchacha20-poly1305</item>
        <item>xchacha20-ietf-poly1305</item>
        <item>none</item>
        <item>plain</item>
        <item>2022-blake3-aes-128-gcm</item>
        <item>2022-blake3-aes-256-gcm</item>
        <item>2022-blake3-chacha20-poly1305</item>
    </string-array>

    <string-array name="networks" translatable="false">
        <item>tcp</item>
        <item>kcp</item>
        <item>ws</item>
        <item>httpupgrade</item>
        <item>xhttp</item>
        <item>h2</item>
        <item>grpc</item>
    </string-array>

    <string-array name="header_type_tcp" translatable="false">
        <item>none</item>
        <item>http</item>
    </string-array>

    <string-array name="header_type_kcp_and_quic" translatable="false">
        <item>none</item>
        <item>srtp</item>
        <item>utp</item>
        <item>wechat-video</item>
        <item>dtls</item>
        <item>wireguard</item>
        <item>dns</item>
    </string-array>

    <string-array name="mode_type_grpc" translatable="false">
        <item>gun</item>
        <item>multi</item>
        <!--Hide this option until core support it       <item>guna</item>-->
    </string-array>

    <string-array name="streamsecuritys" translatable="false">
        <item></item>
        <item>tls</item>
    </string-array>
    <string-array name="streamsecurityxs" translatable="false">
        <item></item>
        <item>tls</item>
        <item>reality</item>
    </string-array>

    <string-array name="fragment_packets" translatable="false">
        <item>tlshello</item>
        <item>1-2</item>
        <item>1-3</item>
        <item>1-5</item>
    </string-array>

    <string-array name="streamsecurity_utls" translatable="false">
        <item></item>
        <item>chrome</item>
        <item>firefox</item>
        <item>safari</item>
        <item>ios</item>
        <item>android</item>
        <item>edge</item>
        <item>360</item>
        <item>qq</item>
        <item>random</item>
        <item>randomized</item>
    </string-array>

    <string-array name="streamsecurity_alpn" translatable="false">
        <item></item>
        <item>h3</item>
        <item>h2</item>
        <item>http/1.1</item>
        <item>h3,h2,http/1.1</item>
        <item>h3,h2</item>
        <item>h2,http/1.1</item>
    </string-array>

    <string-array name="allowinsecures" translatable="false">
        <item></item>
        <item>true</item>
        <item>false</item>
    </string-array>

    <string-array name="routing_domain_strategy" translatable="false">
        <item>AsIs</item>
        <item>IPIfNonMatch</item>
        <item>IPOnDemand</item>
    </string-array>

    <string-array name="core_loglevel" translatable="false">
        <item>debug</item>
        <item>info</item>
        <item>warning</item>
        <item>error</item>
        <item>none</item>
    </string-array>

    <string-array name="mode_value" translatable="false">
        <item>VPN</item>
        <item>Proxy only</item>
    </string-array>

    <string-array name="flows" translatable="false">
        <item></item>
        <item>xtls-rprx-vision</item>
        <item>xtls-rprx-vision-udp443</item>
    </string-array>

    <string-array name="language_select" translatable="false">
        <item>English</item>
        <item>العربية (مصر)</item>
    </string-array>

    <string-array name="share_sub_method" translatable="false">
        <item>QRcode</item>
        <item>Export to clipboard</item>
    </string-array>

    <string-array name="mode_entries" translatable="false">
        <item>VPN</item>
        <item>Proxy only</item>
    </string-array>

    <string-array name="ui_mode_night" translatable="false">
        <item>Follow system</item>
        <item>Light</item>
        <item>Dark</item>
    </string-array>

    <string-array name="preset_rulesets" translatable="false">
        <item>China Whitelist</item>
        <item>China Blacklist</item>
        <item>Global</item>
        <item>Iran Whitelist</item>
    </string-array>

    <string-array name="vpn_bypass_lan" translatable="false">
        <item>Follow config</item>
        <item>Bypass</item>
        <item>Not Bypass</item>
    </string-array>


    <string-array name="language_select_value" translatable="false">
        <item>en</item>
        <item>ar-rEG</item>
    </string-array>

    <string-array name="mux_xudp_quic_value" translatable="false">
        <item>reject</item>
        <item>allow</item>
        <item>skip</item>
    </string-array>

    <string-array name="ui_mode_night_value" translatable="false">
        <item>0</item>
        <item>1</item>
        <item>2</item>
    </string-array>

    <string-array name="outbound_tag" translatable="false">
        <item>proxy</item>
        <item>direct</item>
        <item>block</item>
    </string-array>

    <string-array name="xhttp_mode" translatable="false">
        <item>auto</item>
        <item>packet-up</item>
        <item>stream-up</item>
        <item>stream-one</item>
    </string-array>

    <string-array name="vpn_bypass_lan_value" translatable="false">
        <item>0</item>
        <item>1</item>
        <item>2</item>
    </string-array>

    <!-- Translatable arrays for UI -->
    <string-array name="mux_xudp_quic_entries">
        <item>Reject</item>
        <item>Allow</item>
        <item>Skip</item>
    </string-array>

    <string-array name="routing_domain_strategy_entries">
        <item>As Is</item>
        <item>IP If Non Match</item>
        <item>IP On Demand</item>
    </string-array>

    <string-array name="core_loglevel_entries">
        <item>Debug</item>
        <item>Info</item>
        <item>Warning</item>
        <item>Error</item>
        <item>None</item>
    </string-array>

    <string-array name="outbound_tag_entries">
        <item>Proxy</item>
        <item>Direct</item>
        <item>Block</item>
    </string-array>

    <string-array name="xhttp_mode_entries">
        <item>Auto</item>
        <item>Packet Up</item>
        <item>Stream Up</item>
        <item>Stream One</item>
    </string-array>

    <string-array name="fragment_packets_entries">
        <item>TLS Hello</item>
        <item>1-2</item>
        <item>1-3</item>
        <item>1-5</item>
    </string-array>

    <string-array name="streamsecurity_utls_entries">
        <item>None</item>
        <item>Chrome</item>
        <item>Firefox</item>
        <item>Safari</item>
        <item>iOS</item>
        <item>Android</item>
        <item>Edge</item>
        <item>360</item>
        <item>QQ</item>
        <item>Random</item>
        <item>Randomized</item>
    </string-array>

    <string-array name="streamsecurity_alpn_entries">
        <item>None</item>
        <item>HTTP/3</item>
        <item>HTTP/2</item>
        <item>HTTP/1.1</item>
        <item>HTTP/3, HTTP/2, HTTP/1.1</item>
        <item>HTTP/3, HTTP/2</item>
        <item>HTTP/2, HTTP/1.1</item>
    </string-array>

    <string-array name="flows_entries">
        <item>None</item>
        <item>XTLS-RPRX-Vision</item>
        <item>XTLS-RPRX-Vision-UDP443</item>
    </string-array>

    <string-array name="share_method">
        <item>QR Code</item>
        <item>Export to clipboard</item>
        <item>Export full config to clipboard</item>
    </string-array>

    <string-array name="share_method_more">
        <item>QR Code</item>
        <item>Export to clipboard</item>
        <item>Export full config to clipboard</item>
        <item>Edit</item>
        <item>Delete</item>
    </string-array>

    <string-array name="mode_type_grpc_entries">
        <item>Gun</item>
        <item>Multi</item>
    </string-array>

    <string-array name="streamsecuritys_entries">
        <item>None</item>
        <item>TLS</item>
    </string-array>

    <string-array name="streamsecurityxs_entries">
        <item>None</item>
        <item>TLS</item>
        <item>Reality</item>
    </string-array>

</resources>