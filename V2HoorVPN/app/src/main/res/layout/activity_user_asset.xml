<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:context=".ui.UserAssetActivity">

    <com.google.android.material.progressindicator.LinearProgressIndicator
        android:id="@+id/pb_waiting"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:indeterminate="true"
        android:visibility="invisible"
        app:indicatorColor="@color/color_fab_active" />

    <androidx.core.widget.NestedScrollView
        android:id="@+id/main_content"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/layout_geo_files_sources"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="?attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center|start"
                android:orientation="vertical"
                android:padding="@dimen/padding_spacing_dp16">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/asset_geo_files_sources"
                    android:textAppearance="@style/TextAppearance.AppCompat.Subhead" />

                <TextView
                    android:id="@+id/tv_geo_files_sources_summary"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/padding_spacing_dp8"
                    android:maxLines="2"
                    android:textAppearance="@style/TextAppearance.AppCompat.Small" />
            </LinearLayout>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/padding_spacing_dp16"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/title_user_asset_setting"
                    android:textAppearance="@style/TextAppearance.AppCompat.Subhead" />
            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:nestedScrollingEnabled="false"
                tools:listitem="@layout/item_recycler_user_asset" />
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>


</RelativeLayout>