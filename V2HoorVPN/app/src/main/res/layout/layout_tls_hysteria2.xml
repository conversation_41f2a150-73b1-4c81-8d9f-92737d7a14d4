<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="@dimen/padding_spacing_dp16"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/lay_stream_security"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/padding_spacing_dp16"

        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/server_lab_stream_security" />

        <Spinner
            android:id="@+id/sp_stream_security"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/padding_spacing_dp8"
            android:layout_marginBottom="@dimen/padding_spacing_dp8"
            android:entries="@array/streamsecuritys"
            android:nextFocusDown="@+id/et_sni" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/lay_sni"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/padding_spacing_dp16"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/server_lab_sni" />

        <EditText
            android:id="@+id/et_sni"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="text"
            android:nextFocusDown="@+id/sp_stream_fingerprint" />

    </LinearLayout>


    <LinearLayout
        android:id="@+id/lay_allow_insecure"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/server_lab_allow_insecure" />

        <Spinner
            android:id="@+id/sp_allow_insecure"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/padding_spacing_dp8"
            android:layout_marginBottom="@dimen/padding_spacing_dp8"
            android:entries="@array/allowinsecures" />


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/padding_spacing_dp16"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/server_lab_stream_pinsha256" />

            <EditText
                android:id="@+id/et_pinsha256"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="text" />

        </LinearLayout>
    </LinearLayout>


</LinearLayout>