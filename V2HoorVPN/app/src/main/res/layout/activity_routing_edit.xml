<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:context=".ui.RoutingEditActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="@dimen/padding_spacing_dp16">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/padding_spacing_dp16"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/sub_setting_remarks" />

                <EditText
                    android:id="@+id/et_remarks"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/padding_spacing_dp16"
                android:orientation="vertical">

                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/chk_locked"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/routing_settings_locked"
                    android:textColor="@color/colorAccent"
                    app:theme="@style/BrandedSwitch" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/padding_spacing_dp16"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/routing_settings_rule_title"
                    android:textAppearance="@style/TextAppearance.AppCompat.Subhead" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/padding_spacing_dp16"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/routing_settings_domain" />

                <EditText
                    android:id="@+id/et_domain"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/routing_settings_tips"
                    android:inputType="textMultiLine"
                    android:maxLines="10" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/padding_spacing_dp16"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/routing_settings_ip" />

                <EditText
                    android:id="@+id/et_ip"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/routing_settings_tips"
                    android:inputType="textMultiLine"
                    android:maxLines="10" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/padding_spacing_dp16"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/routing_settings_port" />

                <EditText
                    android:id="@+id/et_port"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/padding_spacing_dp16"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/routing_settings_protocol" />

                <EditText
                    android:id="@+id/et_protocol"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/routing_settings_protocol_tip"
                    android:inputType="text" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/padding_spacing_dp16"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/routing_settings_network" />

                <EditText
                    android:id="@+id/et_network"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/routing_settings_network_tip"
                    android:inputType="text" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/padding_spacing_dp16"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/routing_settings_outbound_tag" />

                <Spinner
                    android:id="@+id/sp_outbound_tag"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/padding_spacing_dp8"
                    android:layout_marginBottom="@dimen/padding_spacing_dp8"
                    android:entries="@array/outbound_tag" />
            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/padding_spacing_dp16"
            android:layout_marginBottom="@dimen/padding_spacing_dp16"
            android:orientation="vertical" />

    </LinearLayout>
</ScrollView>